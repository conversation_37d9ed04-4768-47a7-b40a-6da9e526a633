#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分类目录功能
"""

import os
import shutil

def test_category_directories():
    """测试分类目录结构"""
    
    # 定义分类目录结构
    category_dirs = {
        'gravure': 'gravure_写真精选',
        'japan': 'japan_日系美图',
        'korea': 'korea_韩系风格', 
        'chinese': 'chinese_国风雅韵',
        'cosplay': 'cosplay_角色扮演',
        'thailand': 'thailand_泰式风情'
    }
    
    base_dir = 'downloaded'
    
    print("=== 分类目录测试 ===")
    print(f"基础目录: {base_dir}")
    print()
    
    # 创建测试目录结构
    print("创建测试目录结构...")
    for category, dir_name in category_dirs.items():
        category_path = os.path.join(base_dir, dir_name)
        os.makedirs(category_path, exist_ok=True)
        
        # 创建示例相册目录
        test_album = os.path.join(category_path, f"测试相册_{category}")
        os.makedirs(test_album, exist_ok=True)
        
        # 创建示例图片文件
        for i in range(1, 4):
            test_image = os.path.join(test_album, f"test_image_{i}.jpg")
            with open(test_image, 'w') as f:
                f.write(f"测试图片 {i}")
        
        print(f"✅ 创建分类目录: {dir_name}")
        print(f"   路径: {category_path}")
        print(f"   示例相册: {test_album}")
        print()
    
    # 验证目录结构
    print("验证目录结构...")
    print()
    
    if os.path.exists(base_dir):
        print(f"📁 {base_dir}/")
        for item in sorted(os.listdir(base_dir)):
            item_path = os.path.join(base_dir, item)
            if os.path.isdir(item_path):
                print(f"  📁 {item}/")
                
                # 显示子目录内容
                for sub_item in sorted(os.listdir(item_path)):
                    sub_path = os.path.join(item_path, sub_item)
                    if os.path.isdir(sub_path):
                        print(f"    📁 {sub_item}/")
                        
                        # 显示图片文件
                        images = [f for f in os.listdir(sub_path) 
                                if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp'))]
                        for img in sorted(images):
                            print(f"      🖼️ {img}")
                    else:
                        print(f"    📄 {sub_item}")
    
    print()
    print("=== 目录结构说明 ===")
    print("新的目录结构将按分类组织：")
    print()
    for category, dir_name in category_dirs.items():
        print(f"🏷️ {category.upper()}: downloaded/{dir_name}/")
    
    print()
    print("优势：")
    print("✅ 不同分类内容分开存储")
    print("✅ 便于管理和查找")
    print("✅ 避免内容混杂")
    print("✅ 支持分类标签显示")
    
    print()
    print("✅ 分类目录测试完成！")

def cleanup_test_directories():
    """清理测试目录"""
    base_dir = 'downloaded'
    if os.path.exists(base_dir):
        response = input(f"是否删除测试目录 {base_dir}？(y/N): ")
        if response.lower() == 'y':
            shutil.rmtree(base_dir)
            print(f"✅ 已删除测试目录: {base_dir}")
        else:
            print("保留测试目录")

if __name__ == "__main__":
    test_category_directories()
    print()
    cleanup_test_directories()
