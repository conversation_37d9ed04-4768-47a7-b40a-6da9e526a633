# PicSpider GUI - 写真爬虫图形界面

## 📖 项目简介

PicSpider GUI 是基于原有 PicSpider 项目开发的图形用户界面版本，参考了 `everia_crawler.py` 的设计理念，提供了友好的桌面GUI体验。使用 tkinter 构建，无需额外的Web服务器，可以直接在桌面环境中运行。

## ✨ 主要特性

### 🎯 核心功能
- **图形化界面**：基于 tkinter 的现代化深色主题界面
- **多分类爬取**：支持同时选择多个分类进行爬取
- **实时日志**：彩色日志显示，实时查看爬取进度
- **统计信息**：实时显示下载统计（相册数、图片数、文件大小等）
- **图片浏览**：内置画廊功能，可预览已下载的相册
- **灵活配置**：可调整爬取页数、并发数、保存目录等参数

### 🛠 技术特性
- **多线程下载**：支持并发下载，提高效率
- **智能过滤**：自动过滤无效图片和重复内容
- **错误处理**：完善的异常处理和重试机制
- **进度跟踪**：实时显示当前处理的相册和整体进度
- **安全停止**：支持安全停止爬取任务

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- Windows/Linux/macOS

### 2. 安装依赖

#### 方法一：自动安装（推荐）
```bash
python run_gui.py
```
程序会自动检查并提示安装缺失的依赖包。

#### 方法二：手动安装
```bash
pip install -r requirements_gui.txt
```

### 3. 启动程序

#### 使用启动脚本（推荐）
```bash
python run_gui.py
```

#### 直接启动
```bash
python picspider_gui.py
```

## 📋 使用指南

### 界面布局

程序采用标签页设计，包含三个主要标签页：

#### 1. 爬虫控制台
- **左侧面板**：
  - 分类选择：勾选要爬取的分类
  - 控制按钮：开始/停止爬取
  - 实时统计：显示当前状态和统计信息
- **右侧面板**：
  - 运行日志：彩色日志显示
  - 进度条：显示整体进度

#### 2. 图片浏览
- **工具栏**：刷新画廊、打开文件夹按钮
- **画廊区域**：网格布局显示已下载的相册
- **交互功能**：点击相册可直接打开文件夹

#### 3. 设置
- **爬虫设置**：
  - 最大爬取页数：控制每个分类爬取的页数
  - 下载并发数：控制同时下载的图片数量
  - 保存目录：设置图片保存位置

### 操作流程

1. **配置设置**：
   - 切换到"设置"标签页
   - 根据需要调整爬取页数和并发数
   - 选择合适的保存目录
   - 点击"应用设置"

2. **选择分类**：
   - 在"爬虫控制台"中勾选要爬取的分类
   - 支持多选，可同时爬取多个分类

3. **开始爬取**：
   - 点击"开始爬取"按钮
   - 观察日志输出和统计信息
   - 可随时点击"停止爬取"安全停止

4. **查看结果**：
   - 切换到"图片浏览"标签页
   - 点击"刷新画廊"查看已下载的相册
   - 点击相册缩略图可直接打开文件夹

## ⚙️ 配置说明

### 支持的分类
- **gravure**：写真分类
- **japan**：日本分类
- **korea**：韩国分类
- **chinese**：中文分类
- **cosplay**：角色扮演分类
- **thailand**：泰国分类 ⭐ (新增)

### 参数配置
- **最大爬取页数**：1-50页，默认2页
- **下载并发数**：1-20个，默认5个
- **保存目录**：可自定义，默认为 `downloaded`

## 🎨 界面特色

### 深色主题
- 采用现代化深色主题设计
- 护眼的配色方案
- 清晰的视觉层次

### 彩色日志
- **绿色**：成功信息
- **蓝色**：一般信息  
- **橙色**：警告信息
- **红色**：错误信息

### 响应式布局
- 自适应窗口大小
- 合理的组件布局
- 流畅的用户体验

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查 Python 版本是否为 3.7+
   - 确保所有依赖包已正确安装
   - 使用 `run_gui.py` 自动检查依赖

2. **下载失败**
   - 检查网络连接
   - 确认目标网站可访问
   - 适当降低并发数

3. **界面显示异常**
   - 确保 tkinter 已正确安装
   - 检查系统是否支持图形界面
   - 尝试更新 Pillow 库

### 日志分析
程序会在界面中显示详细的运行日志，包括：
- 请求状态
- 下载进度
- 错误信息
- 统计数据

## 📁 文件结构

```
PicSpider-master/
├── picspider_gui.py          # GUI主程序
├── run_gui.py               # 启动脚本
├── requirements_gui.txt     # GUI依赖包
├── GUI_README.md           # GUI使用说明
├── downloaded/             # 默认下载目录
├── main.py                # 原命令行版本
├── app.py                 # Web展示程序
└── everia_crawler.py      # 参考的Web版爬虫
```

## 🤝 技术支持

如遇到问题，请检查：
1. 依赖包是否完整安装
2. 网络连接是否正常
3. 目标网站是否可访问
4. 系统资源是否充足

## 📄 许可证

本项目基于原 PicSpider 项目开发，遵循相同的开源协议。
