@echo off
chcp 65001 >nul
title PicSpider GUI - 卸载清理工具

:: 设置颜色
color 0C

echo.
echo ========================================
echo    🗑️  PicSpider GUI 卸载清理工具
echo ========================================
echo.

echo ⚠️  警告：此操作将清理PicSpider相关文件和依赖包
echo.
echo 将要执行的操作：
echo 1. 卸载Python依赖包
echo 2. 清理下载的图片文件 (可选)
echo 3. 清理临时文件
echo.

echo 是否继续？(Y/N)
set /p confirm=

if /i not "%confirm%"=="Y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo ========================================
echo    📦 卸载依赖包
echo ========================================
echo.

echo 正在卸载PicSpider相关依赖包...

:: 卸载依赖包（保留常用包）
pip uninstall -y Pillow

echo ✅ 依赖包卸载完成
echo.

echo ========================================
echo    🗂️  清理文件 (可选)
echo ========================================
echo.

echo 是否删除下载的图片文件？(Y/N)
echo 注意：这将删除downloaded文件夹中的所有内容
set /p delete_images=

if /i "%delete_images%"=="Y" (
    if exist "downloaded" (
        echo 正在删除下载文件...
        rmdir /s /q "downloaded"
        echo ✅ 下载文件已删除
    ) else (
        echo ℹ️  未找到下载文件夹
    )
) else (
    echo ℹ️  保留下载文件
)

echo.
echo ========================================
echo    🧹 清理临时文件
echo ========================================
echo.

:: 清理Python缓存
if exist "__pycache__" (
    echo 正在清理Python缓存...
    rmdir /s /q "__pycache__"
    echo ✅ Python缓存已清理
)

:: 清理.pyc文件
echo 正在清理.pyc文件...
del /s /q *.pyc >nul 2>&1

echo ✅ 临时文件清理完成
echo.

echo ========================================
echo    ✅ 清理完成
echo ========================================
echo.

echo 清理操作已完成！
echo.
echo 如需完全移除PicSpider：
echo 1. 手动删除整个PicSpider文件夹
echo 2. 如不再需要Python，可卸载Python环境
echo.

echo 感谢使用PicSpider GUI！
pause
