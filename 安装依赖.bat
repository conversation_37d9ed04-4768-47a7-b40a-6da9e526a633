@echo off
chcp 65001 >nul
title PicSpider GUI - 依赖安装器

:: 设置颜色
color 0E

echo.
echo ========================================
echo    📦 PicSpider GUI 依赖安装器
echo ========================================
echo.

:: 检查Python
echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ 未找到Python环境
    echo.
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

:: 升级pip
echo 正在升级pip...
python -m pip install --upgrade pip

echo.
echo ========================================
echo    📥 安装基础依赖包
echo ========================================
echo.

:: 安装基础依赖
echo 正在安装基础依赖包...
pip install flask requests beautifulsoup4 lxml

if errorlevel 1 (
    echo ❌ 基础依赖安装失败
    pause
    exit /b 1
)

echo ✅ 基础依赖安装完成
echo.

:: 安装Pillow
echo ========================================
echo    🖼️  安装图片处理库 (可选)
echo ========================================
echo.

echo Pillow用于图片预览功能，是否安装？(Y/N)
set /p install_pillow=

if /i "%install_pillow%"=="Y" (
    echo 正在安装Pillow...
    pip install Pillow
    if errorlevel 1 (
        echo ⚠️  Pillow安装失败，但不影响主要功能
    ) else (
        echo ✅ Pillow安装成功
    )
) else (
    echo ⚠️  跳过Pillow安装，图片预览功能将不可用
)

echo.
echo ========================================
echo    ✅ 安装完成
echo ========================================
echo.

echo 所有依赖包安装完成！
echo.
echo 现在您可以：
echo 1. 双击"启动PicSpider.bat"启动程序
echo 2. 双击"快速启动.bat"快速启动
echo 3. 手动执行：python picspider_gui.py
echo.

pause
