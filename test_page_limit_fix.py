#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试页数限制修复效果
"""

import sys
import os

def test_page_limit_fix():
    """测试页数限制修复"""
    
    print("=== 页数限制修复测试 ===")
    print()
    
    # 检查GUI程序中的默认设置
    try:
        # 读取GUI程序文件
        with open('picspider_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查默认页数设置
        if 'self.max_pages = 50' in content:
            print("✅ 默认页数已修复: 50页")
        elif 'self.max_pages = 2' in content:
            print("❌ 默认页数仍为: 2页 (需要修复)")
        else:
            print("⚠️ 未找到默认页数设置")
        
        # 检查页数上限
        if 'to=500' in content:
            print("✅ 页数上限已提高: 500页")
        elif 'to=50' in content:
            print("❌ 页数上限仍为: 50页 (已提高)")
        else:
            print("⚠️ 未找到页数上限设置")
        
        # 检查是否添加了分类信息提示
        if 'category_info' in content and '约270页' in content:
            print("✅ 已添加分类页数信息提示")
        else:
            print("❌ 未添加分类页数信息提示")
        
        print()
        print("=== 修复效果总结 ===")
        print()
        
        print("修复前的问题:")
        print("❌ 默认只爬取2页")
        print("❌ 用户不知道总页数")
        print("❌ 页数上限只有50页")
        print()
        
        print("修复后的改进:")
        print("✅ 默认爬取50页")
        print("✅ 显示每个分类的大致页数")
        print("✅ 页数上限提高到500页")
        print("✅ 用户可以根据需要调整页数")
        print()
        
        print("使用建议:")
        print("🎯 测试用途: 设置5-10页")
        print("🎯 日常使用: 设置50-100页") 
        print("🎯 完整爬取: 设置200-300页")
        print("🎯 特定分类: 根据显示的页数信息调整")
        print()
        
        print("分类页数参考:")
        categories = {
            'gravure': '约270页',
            'japan': '约274页',
            'korea': '约175页', 
            'chinese': '约256页',
            'cosplay': '约115页',
            'thailand': '约50页'
        }
        
        for category, pages in categories.items():
            print(f"  📂 {category}: {pages}")
        
        print()
        print("⚠️ 注意事项:")
        print("1. 页数越多，下载时间越长")
        print("2. 建议先小量测试，再大量下载")
        print("3. 注意磁盘空间和网络流量")
        print("4. 可以随时停止下载任务")
        
    except FileNotFoundError:
        print("❌ 未找到 picspider_gui.py 文件")
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")

def simulate_crawl_info():
    """模拟爬取信息显示"""
    print("\n=== 模拟爬取信息显示 ===")
    print()
    
    print("开始爬取任务...")
    print("选择的分类: gravure, japan")
    print("设置的最大页数: 50 页")
    print()
    print("分类信息:")
    print("  gravure: 约270页 (将爬取前50页)")
    print("  japan: 约274页 (将爬取前50页)")
    print()
    print("预估下载:")
    print("  每页约34个相册")
    print("  50页约1700个相册")
    print("  每个相册约20-50张图片")
    print("  总计约34000-85000张图片")
    print()
    print("⚠️ 这是一个大量下载任务，请确保:")
    print("1. 有足够的磁盘空间 (建议10GB+)")
    print("2. 网络连接稳定")
    print("3. 有足够的时间 (可能需要数小时)")

if __name__ == "__main__":
    test_page_limit_fix()
    simulate_crawl_info()
