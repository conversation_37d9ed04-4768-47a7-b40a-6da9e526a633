@echo off
chcp 65001 >nul
title PicSpider GUI - 快速启动

:: 设置窗口颜色
color 0B

echo.
echo 🕷️ PicSpider GUI - 快速启动
echo ================================
echo.

:: 直接启动程序
echo 正在启动PicSpider GUI...
python picspider_gui.py

:: 如果出错，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 启动失败！
    echo.
    echo 请尝试以下解决方案：
    echo 1. 双击"启动PicSpider.bat"进行完整检查
    echo 2. 手动执行：python picspider_gui.py
    echo 3. 安装依赖：pip install -r requirements_gui.txt
    echo.
    pause
)
