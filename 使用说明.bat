@echo off
chcp 65001 >nul
title PicSpider GUI - 使用说明

:: 设置颜色
color 0F

echo.
echo ========================================
echo    📖 PicSpider GUI 使用说明
echo ========================================
echo.

echo 🕷️ PicSpider GUI 是一个现代化的写真爬虫桌面应用程序
echo.

echo ========================================
echo    🚀 快速开始
echo ========================================
echo.

echo 1. 首次使用：
echo    双击"安装依赖.bat"安装必要的依赖包
echo.

echo 2. 启动程序：
echo    双击"启动PicSpider.bat"（推荐）
echo    或双击"快速启动.bat"（快速启动）
echo.

echo 3. 使用程序：
echo    - 在"设置"标签页配置参数
echo    - 在"爬虫控制台"选择分类并开始爬取
echo    - 在"图片浏览"查看下载的相册
echo.

echo ========================================
echo    📁 文件说明
echo ========================================
echo.

echo 批处理文件：
echo   启动PicSpider.bat  - 完整检查并启动程序
echo   快速启动.bat       - 快速启动程序
echo   安装依赖.bat       - 安装所需依赖包
echo   卸载清理.bat       - 卸载和清理工具
echo   使用说明.bat       - 显示此帮助信息
echo.

echo 程序文件：
echo   picspider_gui.py   - 主程序文件
echo   run_gui.py         - Python启动脚本
echo   requirements_gui.txt - 依赖包列表
echo.

echo 文档文件：
echo   GUI_README.md      - 详细使用指南
echo   GUI_FEATURES.md    - 界面特性说明
echo   PROJECT_SUMMARY.md - 项目技术总结
echo.

echo ========================================
echo    ⚙️ 系统要求
echo ========================================
echo.

echo 操作系统：Windows 7/8/10/11
echo Python版本：3.7 或更高版本
echo 内存要求：至少 4GB RAM
echo 磁盘空间：至少 1GB 可用空间
echo 网络要求：稳定的互联网连接
echo.

echo ========================================
echo    🎯 主要功能
echo ========================================
echo.

echo ✅ 多分类爬取：支持5种不同分类的内容
echo ✅ 实时控制：可随时开始/停止爬取任务
echo ✅ 进度监控：实时显示下载进度和统计信息
echo ✅ 图片浏览：内置相册浏览功能
echo ✅ 参数配置：可调整爬取页数、并发数等
echo ✅ 彩色日志：分类显示运行状态和错误信息
echo.

echo ========================================
echo    🔧 故障排除
echo ========================================
echo.

echo 常见问题：
echo.

echo Q: 程序无法启动？
echo A: 1. 确保Python已正确安装
echo    2. 运行"安装依赖.bat"安装依赖包
echo    3. 检查是否有杀毒软件阻止
echo.

echo Q: 下载速度慢？
echo A: 1. 检查网络连接
echo    2. 在设置中适当调整并发数
echo    3. 避免网络高峰期使用
echo.

echo Q: 图片无法显示？
echo A: 1. 安装Pillow库：pip install Pillow
echo    2. 程序仍可正常工作，只是无缩略图
echo.

echo ========================================
echo    📞 技术支持
echo ========================================
echo.

echo 如遇到问题，请：
echo 1. 查看程序内的彩色日志信息
echo 2. 参考GUI_README.md详细文档
echo 3. 检查PROJECT_SUMMARY.md技术说明
echo.

echo ========================================
echo    📄 许可证
echo ========================================
echo.

echo 本程序基于开源协议发布，可自由使用和修改
echo 请遵守相关法律法规，合理使用爬虫功能
echo.

echo 感谢使用PicSpider GUI！
echo.

pause
