#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PicSpider GUI - 基于tkinter的写真爬虫图形界面
参考everia_crawler.py设计，提供友好的桌面GUI体验
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import queue
import time
import os
import json
import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin
import concurrent.futures
from functools import partial
try:
    from PIL import Image, ImageTk
    PILLOW_AVAILABLE = True
except ImportError:
    PILLOW_AVAILABLE = False
    print("警告: Pillow未安装，图片预览功能将不可用")

import io

class PicSpiderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🌟 PicSpider GUI - 现代化写真爬虫")

        # 优化窗口大小 - 16:10黄金比例，适合大多数屏幕
        self.root.geometry("1600x1000")
        self.root.configure(bg='#0d1117')  # GitHub深色主题背景

        # 设置窗口图标和属性
        self.root.resizable(True, True)
        self.root.minsize(1200, 800)  # 提高最小尺寸

        # 窗口居中显示
        self.center_window()

        # 配置样式
        self.setup_styles()

        # 初始化变量
        self.init_variables()

        # 创建界面
        self.create_widgets()

        # 启动日志处理
        self.process_log_queue()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1600
        height = 1000

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def init_variables(self):
        """初始化变量"""
        self.is_crawling = False
        self.crawl_thread = None
        self.log_queue = queue.Queue()
        self.stats = {
            'total_albums': 0,
            'total_images': 0,
            'total_size': 0,
            'current_album': '',
            'progress': {}
        }
        
        # 爬虫配置
        self.categories = {
            'gravure': 'https://everia.club/category/gravure/',
            'japan': 'https://everia.club/category/japan/',
            'korea': 'https://everia.club/category/korea/',
            'chinese': 'https://everia.club/category/chinese/',
            'cosplay': 'https://everia.club/category/cosplay/',
            'thailand': 'https://everia.club/category/thailand/',
        }
        
        self.save_dir = 'downloaded'
        self.max_pages = 999999  # 默认全部抓取 (无限制)
        self.concurrency = 5  # 并发数

        # 为每个分类设置专门的子目录
        self.category_dirs = {
            'gravure': 'gravure_写真精选',
            'japan': 'japan_日系美图',
            'korea': 'korea_韩系风格',
            'chinese': 'chinese_国风雅韵',
            'cosplay': 'cosplay_角色扮演',
            'thailand': 'thailand_泰式风情'
        }

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_styles(self):
        """设置界面样式 - GitHub深色主题"""
        style = ttk.Style()
        style.theme_use('clam')

        # GitHub深色主题配色
        self.colors = {
            'bg_primary': '#0d1117',      # 主背景 - GitHub深色
            'bg_secondary': '#161b22',    # 次要背景
            'bg_tertiary': '#21262d',     # 第三级背景
            'border': '#30363d',          # 边框颜色
            'text_primary': '#f0f6fc',    # 主要文字
            'text_secondary': '#8b949e',  # 次要文字
            'accent_blue': '#58a6ff',     # 蓝色强调
            'accent_green': '#3fb950',    # 绿色强调
            'accent_orange': '#f85149',   # 橙色强调
            'accent_purple': '#a5a5ff',   # 紫色强调
        }

        # 配置现代化颜色主题
        style.configure('Title.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['accent_blue'],
                       font=('Segoe UI', 20, 'bold'))

        style.configure('Heading.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       font=('Segoe UI', 13, 'bold'))

        style.configure('Info.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_secondary'],
                       font=('Segoe UI', 11))

        # 现代化按钮样式
        style.configure('Modern.TButton',
                       background=self.colors['accent_blue'],
                       foreground=self.colors['text_primary'],
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 11, 'bold'),
                       padding=(20, 10))

        style.map('Modern.TButton',
                 background=[('active', '#4493f8'),
                           ('pressed', '#316dca')])

        # 成功按钮样式
        style.configure('Success.TButton',
                       background=self.colors['accent_green'],
                       foreground=self.colors['text_primary'],
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 11, 'bold'),
                       padding=(20, 10))

        # 危险按钮样式
        style.configure('Danger.TButton',
                       background=self.colors['accent_orange'],
                       foreground=self.colors['text_primary'],
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 11, 'bold'),
                       padding=(20, 10))

        # Notebook样式
        style.configure('Modern.TNotebook',
                       background=self.colors['bg_primary'],
                       borderwidth=0,
                       tabmargins=[2, 5, 2, 0])

        style.configure('Modern.TNotebook.Tab',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_secondary'],
                       padding=[20, 10],
                       font=('Segoe UI', 10, 'bold'))

        style.map('Modern.TNotebook.Tab',
                 background=[('selected', '#0078d4'),
                           ('active', '#3d3d3d')],
                 foreground=[('selected', 'white')])

        # 进度条样式
        style.configure('Modern.Horizontal.TProgressbar',
                       background='#0078d4',
                       troughcolor='#3d3d3d',
                       borderwidth=0,
                       lightcolor='#0078d4',
                       darkcolor='#0078d4')
    
    def create_widgets(self):
        """创建界面组件"""
        # 现代化标题栏
        title_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=90)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)

        # 标题容器
        title_container = tk.Frame(title_frame, bg=self.colors['bg_primary'])
        title_container.pack(expand=True, fill='both')

        # 主标题
        main_title = tk.Label(title_container,
                             text="🌟 PicSpider GUI",
                             bg=self.colors['bg_primary'],
                             fg=self.colors['accent_blue'],
                             font=('Segoe UI', 26, 'bold'))
        main_title.pack(side='left', padx=25, pady=20)

        # 副标题
        sub_title = tk.Label(title_container,
                            text="现代化写真爬虫 | 智能下载 | 实时预览 | 无限制抓取",
                            bg=self.colors['bg_primary'],
                            fg=self.colors['text_secondary'],
                            font=('Segoe UI', 12))
        sub_title.pack(side='left', padx=(0, 25), pady=25)

        # 状态指示器
        self.status_indicator = tk.Label(title_container,
                                        text="● 就绪",
                                        bg=self.colors['bg_primary'],
                                        fg=self.colors['accent_green'],
                                        font=('Segoe UI', 13, 'bold'))
        self.status_indicator.pack(side='right', padx=25, pady=25)

        # 分隔线
        separator = tk.Frame(self.root, bg=self.colors['accent_blue'], height=3)
        separator.pack(fill='x')

        # 创建现代化Notebook（标签页）
        self.notebook = ttk.Notebook(self.root, style='Modern.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=15, pady=15)
        
        # 爬虫控制台标签页
        self.create_crawler_tab()
        
        # 图片浏览标签页
        self.create_gallery_tab()
        
        # 设置标签页
        self.create_settings_tab()
    
    def create_crawler_tab(self):
        """创建爬虫控制台标签页"""
        crawler_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(crawler_frame, text="🚀 爬虫控制台")

        # 左侧控制面板 - 现代化卡片设计
        left_frame = tk.Frame(crawler_frame, bg=self.colors['bg_primary'], width=380)
        left_frame.pack(side='left', fill='y', padx=15, pady=15)
        left_frame.pack_propagate(False)
        
        # 分类选择 - 现代化卡片
        category_card = tk.Frame(left_frame, bg=self.colors['bg_secondary'], relief='flat', bd=0)
        category_card.pack(fill='x', pady=(0, 20))

        # 卡片标题
        category_title = tk.Label(category_card, text="📂 选择分类",
                                bg=self.colors['bg_secondary'],
                                fg=self.colors['accent_blue'],
                                font=('Segoe UI', 14, 'bold'))
        category_title.pack(anchor='w', padx=20, pady=(20, 15))

        # 分类选项
        self.category_vars = {}
        category_options = {
            'gravure': '🌸 写真精选',
            'japan': '🇯🇵 日系美图',
            'korea': '🇰🇷 韩系风格',
            'chinese': '🇨🇳 国风雅韵',
            'cosplay': '🎭 角色扮演',
            'thailand': '🇹🇭 泰式风情'
        }

        for category, display_name in category_options.items():
            var = tk.BooleanVar()
            self.category_vars[category] = var

            cb_frame = tk.Frame(category_card, bg='#2d2d2d')
            cb_frame.pack(fill='x', padx=15, pady=2)

            cb = tk.Checkbutton(cb_frame, text=display_name,
                              variable=var, bg='#2d2d2d', fg='#ffffff',
                              selectcolor='#0078d4', activebackground='#3d3d3d',
                              activeforeground='#ffffff', font=('Segoe UI', 10),
                              borderwidth=0, highlightthickness=0)
            cb.pack(anchor='w')

        # 底部间距
        tk.Frame(category_card, bg='#2d2d2d', height=10).pack()
        
        # 控制按钮 - 现代化卡片
        control_card = tk.Frame(left_frame, bg='#2d2d2d', relief='flat', bd=0)
        control_card.pack(fill='x', pady=(0, 15))

        # 卡片标题
        control_title = tk.Label(control_card, text="⚡ 控制操作",
                               bg='#2d2d2d', fg='#00d4ff',
                               font=('Segoe UI', 12, 'bold'))
        control_title.pack(anchor='w', padx=15, pady=(15, 10))

        # 按钮容器
        btn_container = tk.Frame(control_card, bg='#2d2d2d')
        btn_container.pack(fill='x', padx=15, pady=(0, 15))

        # 开始按钮 - 现代化设计
        self.start_btn = tk.Button(btn_container, text="🚀 开始爬取",
                                 command=self.start_crawling,
                                 bg='#4caf50', fg='white',
                                 font=('Segoe UI', 11, 'bold'),
                                 relief='flat', bd=0, cursor='hand2',
                                 activebackground='#45a049')
        self.start_btn.pack(fill='x', pady=(0, 8))

        # 停止按钮
        self.stop_btn = tk.Button(btn_container, text="⏹️ 停止爬取",
                                command=self.stop_crawling,
                                bg='#f44336', fg='white',
                                font=('Segoe UI', 11, 'bold'),
                                relief='flat', bd=0, cursor='hand2',
                                activebackground='#da190b',
                                state='disabled')
        self.stop_btn.pack(fill='x')
        
        # 统计信息 - 现代化卡片
        stats_card = tk.Frame(left_frame, bg='#2d2d2d', relief='flat', bd=0)
        stats_card.pack(fill='x', pady=(0, 15))

        # 卡片标题
        stats_title = tk.Label(stats_card, text="📊 实时统计",
                             bg='#2d2d2d', fg='#00d4ff',
                             font=('Segoe UI', 12, 'bold'))
        stats_title.pack(anchor='w', padx=15, pady=(15, 10))

        # 统计项目
        self.stats_labels = {}
        stats_items = [
            ('🔄 状态', 'status', '#4caf50'),
            ('📁 当前相册', 'current_album', '#ff9800'),
            ('📚 下载相册', 'total_albums', '#2196f3'),
            ('🖼️ 下载图片', 'total_images', '#9c27b0'),
            ('💾 总大小', 'total_size', '#00bcd4')
        ]

        for label_text, key, color in stats_items:
            item_frame = tk.Frame(stats_card, bg='#2d2d2d')
            item_frame.pack(fill='x', padx=15, pady=3)

            # 左侧标签
            label = tk.Label(item_frame, text=label_text,
                           bg='#2d2d2d', fg='#e0e0e0',
                           font=('Segoe UI', 10))
            label.pack(side='left')

            # 右侧数值
            value_label = tk.Label(item_frame, text="就绪" if key == 'status' else "0",
                                 bg='#2d2d2d', fg=color,
                                 font=('Segoe UI', 10, 'bold'))
            value_label.pack(side='right')
            self.stats_labels[key] = value_label

        # 底部间距
        tk.Frame(stats_card, bg='#2d2d2d', height=10).pack()
        
        # 右侧日志面板 - 现代化设计
        right_frame = tk.Frame(crawler_frame, bg='#1e1e1e')
        right_frame.pack(side='right', fill='both', expand=True, padx=(0, 10), pady=10)

        # 日志卡片
        log_card = tk.Frame(right_frame, bg='#2d2d2d', relief='flat', bd=0)
        log_card.pack(fill='both', expand=True, pady=(0, 10))

        # 日志标题栏
        log_header = tk.Frame(log_card, bg='#2d2d2d', height=50)
        log_header.pack(fill='x')
        log_header.pack_propagate(False)

        log_title = tk.Label(log_header, text="📝 运行日志",
                           bg='#2d2d2d', fg='#00d4ff',
                           font=('Segoe UI', 12, 'bold'))
        log_title.pack(side='left', padx=15, pady=15)

        # 清空日志按钮
        clear_btn = tk.Button(log_header, text="🗑️ 清空",
                            command=self.clear_log,
                            bg='#444444', fg='white',
                            font=('Segoe UI', 9),
                            relief='flat', bd=0, cursor='hand2',
                            activebackground='#555555')
        clear_btn.pack(side='right', padx=15, pady=12)

        # 日志文本区域
        log_container = tk.Frame(log_card, bg='#2d2d2d')
        log_container.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        self.log_text = scrolledtext.ScrolledText(log_container,
                                                bg='#1a1a1a', fg='#e0e0e0',
                                                font=('JetBrains Mono', 9),
                                                wrap='word', relief='flat', bd=0,
                                                insertbackground='#00d4ff')
        self.log_text.pack(fill='both', expand=True)

        # 进度条卡片
        progress_card = tk.Frame(right_frame, bg='#2d2d2d', relief='flat', bd=0, height=60)
        progress_card.pack(fill='x')
        progress_card.pack_propagate(False)

        progress_container = tk.Frame(progress_card, bg='#2d2d2d')
        progress_container.pack(expand=True, fill='both', padx=15, pady=15)

        progress_label = tk.Label(progress_container, text="⚡ 总体进度:",
                                bg='#2d2d2d', fg='#e0e0e0',
                                font=('Segoe UI', 10, 'bold'))
        progress_label.pack(side='left')

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_container,
                                          variable=self.progress_var,
                                          maximum=100,
                                          style='Modern.Horizontal.TProgressbar')
        self.progress_bar.pack(side='right', fill='x', expand=True, padx=(15, 0))
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete('1.0', tk.END)
        self.log_message("日志已清空", 'info')

    def create_gallery_tab(self):
        """创建图片浏览标签页"""
        gallery_frame = tk.Frame(self.notebook, bg='#1e1e1e')
        self.notebook.add(gallery_frame, text="🖼️ 图片浏览")
        
        # 现代化工具栏
        toolbar = tk.Frame(gallery_frame, bg='#2d2d2d', height=70)
        toolbar.pack(fill='x', padx=15, pady=(15, 10))
        toolbar.pack_propagate(False)

        # 工具栏容器
        toolbar_container = tk.Frame(toolbar, bg='#2d2d2d')
        toolbar_container.pack(expand=True, fill='both', padx=15, pady=15)

        # 标题
        gallery_title = tk.Label(toolbar_container, text="🖼️ 相册画廊",
                               bg='#2d2d2d', fg='#00d4ff',
                               font=('Segoe UI', 14, 'bold'))
        gallery_title.pack(side='left')

        # 按钮组
        btn_group = tk.Frame(toolbar_container, bg='#2d2d2d')
        btn_group.pack(side='right')

        # 刷新按钮
        refresh_btn = tk.Button(btn_group, text="🔄 刷新画廊",
                              command=self.refresh_gallery,
                              bg='#0078d4', fg='white',
                              font=('Segoe UI', 10, 'bold'),
                              relief='flat', bd=0, cursor='hand2',
                              activebackground='#106ebe')
        refresh_btn.pack(side='left', padx=(0, 10))

        # 打开文件夹按钮
        folder_btn = tk.Button(btn_group, text="📁 打开文件夹",
                             command=self.open_download_folder,
                             bg='#ff9800', fg='white',
                             font=('Segoe UI', 10, 'bold'),
                             relief='flat', bd=0, cursor='hand2',
                             activebackground='#f57c00')
        folder_btn.pack(side='left')
        
        # 现代化画廊区域
        gallery_container = tk.Frame(gallery_frame, bg='#2d2d2d', relief='flat', bd=0)
        gallery_container.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        # 画廊画布
        self.gallery_canvas = tk.Canvas(gallery_container, bg='#1e1e1e',
                                      highlightthickness=0, relief='flat', bd=0)

        # 现代化滚动条
        gallery_scrollbar = ttk.Scrollbar(gallery_container,
                                        orient="vertical",
                                        command=self.gallery_canvas.yview)
        self.gallery_canvas.configure(yscrollcommand=gallery_scrollbar.set)

        gallery_scrollbar.pack(side="right", fill="y", padx=(5, 10), pady=10)
        self.gallery_canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)

        # 画廊内容框架
        self.gallery_frame = tk.Frame(self.gallery_canvas, bg='#1e1e1e')
        self.gallery_canvas.create_window((0, 0), window=self.gallery_frame, anchor="nw")

        # 绑定事件
        self.gallery_canvas.bind("<Configure>", self.on_gallery_configure)
        self.gallery_canvas.bind_all("<MouseWheel>", self.on_mousewheel)
    
    def create_settings_tab(self):
        """创建设置标签页"""
        settings_frame = tk.Frame(self.notebook, bg='#1e1e1e')
        self.notebook.add(settings_frame, text="⚙️ 设置")

        # 设置容器
        settings_container = tk.Frame(settings_frame, bg='#1e1e1e')
        settings_container.pack(fill='both', expand=True, padx=20, pady=20)

        # 爬虫设置卡片
        crawler_card = tk.Frame(settings_container, bg='#2d2d2d', relief='flat', bd=0)
        crawler_card.pack(fill='x', pady=(0, 20))

        # 卡片标题
        settings_title = tk.Label(crawler_card, text="🚀 爬虫设置",
                                bg='#2d2d2d', fg='#00d4ff',
                                font=('Segoe UI', 14, 'bold'))
        settings_title.pack(anchor='w', padx=20, pady=(20, 15))
        
        # 设置项目容器
        settings_content = tk.Frame(crawler_card, bg='#2d2d2d')
        settings_content.pack(fill='x', padx=20, pady=(0, 20))

        # 最大页数设置
        pages_item = tk.Frame(settings_content, bg='#2d2d2d')
        pages_item.pack(fill='x', pady=(0, 15))

        pages_label = tk.Label(pages_item, text="📄 最大爬取页数:",
                             bg='#2d2d2d', fg='#ffffff',
                             font=('Segoe UI', 11))
        pages_label.pack(side='left')

        # 页数设置框架
        pages_control_frame = tk.Frame(pages_item, bg='#2d2d2d')
        pages_control_frame.pack(side='right')

        # 全部抓取复选框
        self.all_pages_var = tk.BooleanVar(value=True)  # 默认全部抓取
        all_pages_check = tk.Checkbutton(pages_control_frame, text="全部抓取",
                                       variable=self.all_pages_var,
                                       bg='#2d2d2d', fg='#00d4ff',
                                       selectcolor='#444444',
                                       font=('Segoe UI', 9),
                                       command=self.toggle_page_limit)
        all_pages_check.pack(side='left', padx=(0, 10))

        # 页数输入框
        self.pages_var = tk.IntVar(value=50)  # 显示值为50，但实际使用时会检查全部抓取选项
        self.pages_spinbox = tk.Spinbox(pages_control_frame, from_=1, to=99999,
                                      textvariable=self.pages_var,
                                      width=8, bg='#444444', fg='#ffffff',
                                      font=('Segoe UI', 10), relief='flat', bd=0,
                                      buttonbackground='#0078d4',
                                      selectbackground='#0078d4',
                                      state='disabled')  # 默认禁用，因为选择了全部抓取
        self.pages_spinbox.pack(side='right')

        # 并发数设置
        concurrency_item = tk.Frame(settings_content, bg='#2d2d2d')
        concurrency_item.pack(fill='x', pady=(0, 15))

        concurrency_label = tk.Label(concurrency_item, text="⚡ 下载并发数:",
                                    bg='#2d2d2d', fg='#ffffff',
                                    font=('Segoe UI', 11))
        concurrency_label.pack(side='left')

        self.concurrency_var = tk.IntVar(value=self.concurrency)
        concurrency_spinbox = tk.Spinbox(concurrency_item, from_=1, to=20,
                                       textvariable=self.concurrency_var,
                                       width=8, bg='#444444', fg='#ffffff',
                                       font=('Segoe UI', 10), relief='flat', bd=0,
                                       buttonbackground='#0078d4',
                                       selectbackground='#0078d4')
        concurrency_spinbox.pack(side='right')

        # 保存目录设置
        dir_item = tk.Frame(settings_content, bg='#2d2d2d')
        dir_item.pack(fill='x', pady=(0, 20))

        dir_label = tk.Label(dir_item, text="📁 保存目录:",
                           bg='#2d2d2d', fg='#ffffff',
                           font=('Segoe UI', 11))
        dir_label.pack(anchor='w', pady=(0, 8))

        dir_input_frame = tk.Frame(dir_item, bg='#2d2d2d')
        dir_input_frame.pack(fill='x')

        self.dir_var = tk.StringVar(value=self.save_dir)
        dir_entry = tk.Entry(dir_input_frame, textvariable=self.dir_var,
                           bg='#444444', fg='#ffffff', font=('Segoe UI', 10),
                           relief='flat', bd=0, insertbackground='#00d4ff')
        dir_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = tk.Button(dir_input_frame, text="📁 浏览",
                             command=self.browse_directory,
                             bg='#0078d4', fg='white',
                             font=('Segoe UI', 10, 'bold'),
                             relief='flat', bd=0, cursor='hand2',
                             activebackground='#106ebe')
        browse_btn.pack(side='right')

        # 应用设置按钮
        apply_btn = tk.Button(settings_content, text="✅ 应用设置",
                            command=self.apply_settings,
                            bg='#4caf50', fg='white',
                            font=('Segoe UI', 12, 'bold'),
                            relief='flat', bd=0, cursor='hand2',
                            activebackground='#45a049')
        apply_btn.pack(pady=(10, 0))

    def log_message(self, message, level='info'):
        """添加日志消息到队列"""
        timestamp = time.strftime("%H:%M:%S")
        color_map = {
            'info': '#e0e0e0',
            'success': '#4caf50',
            'error': '#f44336',
            'warning': '#ff9800',
            'primary': '#00bcd4'
        }
        color = color_map.get(level, '#e0e0e0')
        self.log_queue.put((f"[{timestamp}] {message}", color))

    def process_log_queue(self):
        """处理日志队列"""
        try:
            while True:
                message, color = self.log_queue.get_nowait()
                self.log_text.insert(tk.END, message + '\n')

                # 设置颜色
                start_line = self.log_text.index(tk.END + "-2l")
                end_line = self.log_text.index(tk.END + "-1l")
                self.log_text.tag_add(f"color_{color}", start_line, end_line)
                self.log_text.tag_config(f"color_{color}", foreground=color)

                # 自动滚动到底部
                self.log_text.see(tk.END)

                # 限制日志行数
                lines = int(self.log_text.index('end-1c').split('.')[0])
                if lines > 1000:
                    self.log_text.delete('1.0', '100.0')
        except queue.Empty:
            pass

        # 每100ms检查一次
        self.root.after(100, self.process_log_queue)

    def update_stats_display(self):
        """更新统计信息显示"""
        # 更新状态指示器
        if self.is_crawling:
            self.status_indicator.config(text="● 运行中", fg='#ff9800')
            self.stats_labels['status'].config(text="运行中", fg='#ff9800')
        else:
            self.status_indicator.config(text="● 就绪", fg='#4caf50')
            self.stats_labels['status'].config(text="就绪", fg='#4caf50')

        # 更新当前相册（截断长名称）
        current_album = self.stats['current_album']
        if len(current_album) > 20:
            current_album = current_album[:17] + "..."
        self.stats_labels['current_album'].config(text=current_album or "无")

        # 更新数值统计
        self.stats_labels['total_albums'].config(text=str(self.stats['total_albums']))
        self.stats_labels['total_images'].config(text=str(self.stats['total_images']))

        # 格式化文件大小
        size = self.stats['total_size']
        if size < 1024:
            size_str = f"{size} B"
        elif size < 1024 * 1024:
            size_str = f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            size_str = f"{size / (1024 * 1024):.1f} MB"
        else:
            size_str = f"{size / (1024 * 1024 * 1024):.1f} GB"

        self.stats_labels['total_size'].config(text=size_str)

    def start_crawling(self):
        """开始爬取"""
        # 检查是否选择了分类
        selected_categories = [cat for cat, var in self.category_vars.items() if var.get()]
        if not selected_categories:
            messagebox.showwarning("警告", "请至少选择一个分类！")
            return

        # 更新UI状态
        self.is_crawling = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')

        # 清空日志
        self.log_text.delete('1.0', tk.END)

        # 重置统计
        self.stats = {
            'total_albums': 0,
            'total_images': 0,
            'total_size': 0,
            'current_album': '',
            'progress': {}
        }
        self.update_stats_display()

        self.log_message("开始爬取任务...", 'success')
        self.log_message(f"选择的分类: {', '.join(selected_categories)}", 'info')

        # 显示爬取范围
        if self.max_pages >= 999999:
            self.log_message("爬取范围: 🌟 全部页面 (无限制)", 'primary')
        else:
            self.log_message(f"爬取范围: 前 {self.max_pages} 页", 'info')

        # 显示分类的大致页数信息
        category_info = {
            'gravure': '约270页',
            'japan': '约274页',
            'korea': '约175页',
            'chinese': '约256页',
            'cosplay': '约115页',
            'thailand': '约50页'
        }

        for category in selected_categories:
            if category in category_info:
                if self.max_pages >= 999999:
                    self.log_message(f"  📂 {category}: {category_info[category]} (🌟 全部抓取)", 'primary')
                else:
                    self.log_message(f"  📂 {category}: {category_info[category]} (将爬取前{self.max_pages}页)", 'info')

        # 启动爬取线程
        self.crawl_thread = threading.Thread(
            target=self.crawl_worker,
            args=(selected_categories,),
            daemon=True
        )
        self.crawl_thread.start()

    def stop_crawling(self):
        """停止爬取"""
        self.is_crawling = False
        self.log_message("收到停止指令，正在安全退出...", 'warning')

        # 更新UI状态
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.update_stats_display()

    def toggle_page_limit(self):
        """切换页数限制"""
        if self.all_pages_var.get():
            # 全部抓取
            self.pages_spinbox.config(state='disabled')
            self.max_pages = 999999
        else:
            # 限制页数
            self.pages_spinbox.config(state='normal')
            self.max_pages = self.pages_var.get()

    def apply_settings(self):
        """应用设置"""
        if self.all_pages_var.get():
            self.max_pages = 999999  # 全部抓取
        else:
            self.max_pages = self.pages_var.get()

        self.concurrency = self.concurrency_var.get()
        self.save_dir = self.dir_var.get()

        pages_text = "全部页面" if self.max_pages >= 999999 else f"{self.max_pages}页"
        self.log_message("设置已应用", 'success')
        self.log_message(f"爬取范围: {pages_text}, 并发数: {self.concurrency}", 'info')
        self.log_message(f"保存目录: {self.save_dir}", 'info')

    def browse_directory(self):
        """浏览目录"""
        directory = filedialog.askdirectory(initialdir=self.save_dir)
        if directory:
            self.dir_var.set(directory)

    def refresh_gallery(self):
        """刷新画廊"""
        self.log_message("正在刷新画廊...", 'info')
        threading.Thread(target=self.load_gallery, daemon=True).start()

    def open_download_folder(self):
        """打开下载文件夹"""
        if os.path.exists(self.save_dir):
            os.startfile(self.save_dir)  # Windows
        else:
            messagebox.showinfo("提示", f"目录不存在: {self.save_dir}")

    def on_gallery_configure(self, event):
        """画廊配置事件"""
        self.gallery_canvas.configure(scrollregion=self.gallery_canvas.bbox("all"))

    def on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.gallery_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def clean_filename(self, text):
        """清理文件名"""
        return re.sub(r'[\\/*?:"<>|]', '_', text).strip()

    def get_target_links(self, start_url):
        """获取目标链接"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://everia.club/'
        }

        try:
            self.log_message(f"正在请求: {start_url}", 'info')
            response = requests.get(start_url, headers=headers, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            target_links = []
            for a_tag in soup.select('a.thumbnail-link'):
                # 检查父级div的完整class名称
                parent_div = a_tag.find_parent('div', class_='post_thumb post_thumb_top')
                if parent_div:
                    continue

                img_tag = a_tag.find('img')
                if img_tag:
                    # 检查图片的特定属性组合
                    if (img_tag.get('post-id') and
                        img_tag.get('fifu-featured') == '1' and
                        img_tag.get('width') == '360' and
                        img_tag.get('height') == '540'):
                        continue

                    href = a_tag.get('href')
                    title = img_tag.get('title', '未命名')
                    if href:
                        target_links.append({
                            'url': urljoin(start_url, href),
                            'title': title
                        })

            self.log_message(f"找到 {len(target_links)} 个有效链接", 'success')
            return target_links
        except Exception as e:
            self.log_message(f"获取链接失败: {str(e)}", 'error')
            return []

    def download_image(self, url, headers, folder_name, save_dir, retry=3):
        """下载单张图片"""
        if not self.is_crawling:
            return False

        for attempt in range(retry):
            try:
                response = requests.get(url, headers=headers, timeout=30, verify=False)
                if response.status_code == 200:
                    img_data = response.content
                    filename = os.path.join(save_dir, folder_name, f'{folder_name}_{url.split("/")[-1]}')

                    # 确保目录存在
                    os.makedirs(os.path.dirname(filename), exist_ok=True)

                    with open(filename, 'wb') as f:
                        f.write(img_data)

                    # 更新统计
                    self.stats['total_images'] += 1
                    self.stats['total_size'] += len(img_data)

                    return True
                else:
                    self.log_message(f"服务器响应错误 {response.status_code}: {url}", 'warning')
            except Exception as e:
                if attempt == retry - 1:
                    self.log_message(f"下载失败: {url} - {str(e)}", 'error')
                    return False
                time.sleep(2 + attempt)

        return False

    def download_images(self, page_info, save_dir):
        """下载相册图片"""
        if not self.is_crawling:
            return False

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': page_info['url'],
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }

        folder_name = self.clean_filename(page_info['title'])
        folder_path = os.path.join(save_dir, folder_name)

        # 检查文件夹是否已存在
        if os.path.exists(folder_path) and os.path.isdir(folder_path):
            self.log_message(f"文件夹已存在，跳过: {folder_name}", 'warning')
            return True

        self.stats['current_album'] = folder_name
        self.root.after(0, self.update_stats_display)

        try:
            response = requests.get(page_info['url'], headers=headers, timeout=30, verify=False)
            soup = BeautifulSoup(response.text, 'html.parser')

            image_urls = []

            # 收集所有图片URL
            for img in soup.select('img[src*="/wp-content/uploads/"]'):
                src = img.get('src') or img.get('data-src')
                if src:
                    filename = src.split('/')[-1].lower()

                    # 跳过特定格式的文件名
                    if (filename.startswith('0') or
                        '_0.' in filename or
                        filename.endswith('0.jpg') or
                        filename.endswith('0.jpeg')):
                        continue

                    # 只接受类似 NAME_NUMBER.jpg 格式的文件
                    if re.match(r'^[a-zA-Z0-9]+_[1-9]\d*\.(jpg|jpeg|png)$', filename):
                        image_urls.append(src)

            # 去重
            final_urls = list(set(image_urls))

            if not final_urls:
                self.log_message(f"未找到有效图片: {folder_name}", 'warning')
                return False

            self.log_message(f"开始下载 {folder_name}: {len(final_urls)} 张图片", 'primary')

            # 使用线程池并行下载
            success_count = 0
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.concurrency) as executor:
                download_func = partial(self.download_image, headers=headers,
                                     folder_name=folder_name, save_dir=save_dir)

                futures = []
                for url in final_urls:
                    if not self.is_crawling:
                        break
                    futures.append(executor.submit(download_func, url))
                    time.sleep(0.1)  # 小延迟避免过快请求

                # 等待所有任务完成
                for future in concurrent.futures.as_completed(futures):
                    if not self.is_crawling:
                        break
                    try:
                        if future.result():
                            success_count += 1
                    except Exception as e:
                        self.log_message(f"下载任务异常: {str(e)}", 'error')

            if success_count > 0:
                self.stats['total_albums'] += 1
                self.log_message(f"完成下载 {folder_name}: {success_count}/{len(final_urls)} 张图片", 'success')
                return True
            else:
                self.log_message(f"下载失败: {folder_name}", 'error')
                return False

        except Exception as e:
            self.log_message(f"处理页面失败 {page_info['url']}: {str(e)}", 'error')
            return False

    def get_next_page(self, soup, base_url):
        """获取下一页链接"""
        next_link = soup.select_one('a.next.page-numbers')
        return urljoin(base_url, next_link['href']) if next_link else None

    def process_category(self, base_url, category_name, max_pages):
        """处理单个分类"""
        current_url = base_url
        page_count = 0

        # 为当前分类创建专用目录
        category_dir = self.category_dirs.get(category_name, category_name)
        category_save_dir = os.path.join(self.save_dir, category_dir)

        # 确保分类目录存在
        os.makedirs(category_save_dir, exist_ok=True)

        self.log_message(f"分类 {category_name} 将保存到: {category_save_dir}", 'info')

        while current_url and page_count < max_pages and self.is_crawling:
            self.log_message(f"正在处理 {category_name} 第 {page_count + 1} 页", 'info')
            target_pages = self.get_target_links(current_url)

            if not target_pages:
                self.log_message("没有找到目标页面，跳过此页", 'warning')
                break

            for page in target_pages:
                if not self.is_crawling:
                    break

                self.log_message(f"处理: {page['title']}", 'info')
                success = self.download_images(page, category_save_dir)

                # 更新UI
                self.root.after(0, self.update_stats_display)

                if success:
                    self.log_message(f"成功处理: {page['title']}", 'success')

                time.sleep(1)  # 避免请求过快

            # 获取下一页
            try:
                response = requests.get(current_url, timeout=10)
                soup = BeautifulSoup(response.text, 'html.parser')
                current_url = self.get_next_page(soup, current_url)
            except Exception as e:
                self.log_message(f"获取下一页失败: {str(e)}", 'error')
                break

            page_count += 1
            time.sleep(2)  # 页面间延迟

    def crawl_worker(self, selected_categories):
        """爬虫工作线程"""
        try:
            for category in selected_categories:
                if not self.is_crawling:
                    break

                base_url = self.categories.get(category)
                if not base_url:
                    continue

                self.log_message(f"开始处理分类: {category}", 'primary')
                self.process_category(base_url, category, self.max_pages)

                if self.is_crawling:
                    self.log_message(f"完成分类: {category}", 'success')

            if self.is_crawling:
                self.log_message("所有爬取任务完成！", 'success')
            else:
                self.log_message("爬取任务已停止", 'warning')

        except Exception as e:
            self.log_message(f"爬取过程出错: {str(e)}", 'error')
        finally:
            # 重置状态
            self.is_crawling = False
            self.stats['current_album'] = ''
            self.root.after(0, lambda: [
                self.start_btn.config(state='normal'),
                self.stop_btn.config(state='disabled'),
                self.update_stats_display()
            ])

    def load_gallery(self):
        """加载画廊"""
        try:
            # 清空现有画廊
            for widget in self.gallery_frame.winfo_children():
                widget.destroy()

            if not os.path.exists(self.save_dir):
                tk.Label(self.gallery_frame, text="下载目录不存在",
                        bg='#1a1a1a', fg='#e0e0e0').pack(pady=20)
                return

            albums = []

            # 扫描主目录和所有分类子目录
            def scan_directory(scan_path, category_name=None):
                if not os.path.exists(scan_path):
                    return

                for item in os.listdir(scan_path):
                    item_path = os.path.join(scan_path, item)
                    if os.path.isdir(item_path):
                        # 获取第一张图片作为缩略图
                        images = [f for f in os.listdir(item_path)
                                 if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp'))]
                        if images:
                            cover_path = os.path.join(item_path, sorted(images)[0])
                            album_name = item
                            if category_name:
                                album_name = f"[{category_name}] {item}"
                            albums.append({
                                'name': album_name,
                                'path': item_path,
                                'cover': cover_path,
                                'count': len(images),
                                'category': category_name or '未分类'
                            })

            # 扫描主目录中的直接相册
            scan_directory(self.save_dir)

            # 扫描各个分类目录
            for category_key, category_dir in self.category_dirs.items():
                category_path = os.path.join(self.save_dir, category_dir)
                category_display_name = {
                    'gravure': '写真精选',
                    'japan': '日系美图',
                    'korea': '韩系风格',
                    'chinese': '国风雅韵',
                    'cosplay': '角色扮演',
                    'thailand': '泰式风情'
                }.get(category_key, category_key)
                scan_directory(category_path, category_display_name)

            if not albums:
                tk.Label(self.gallery_frame, text="暂无相册",
                        bg='#1a1a1a', fg='#e0e0e0').pack(pady=20)
                return

            # 创建相册卡片
            row = 0
            col = 0
            max_cols = 4

            for album in albums:
                try:
                    # 创建现代化相册卡片
                    album_frame = tk.Frame(self.gallery_frame, bg='#2d2d2d',
                                         relief='flat', bd=0)
                    album_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

                    # 添加悬停效果
                    def on_enter(e):
                        album_frame.config(bg='#3d3d3d')
                        for child in album_frame.winfo_children():
                            if isinstance(child, tk.Frame):
                                child.config(bg='#3d3d3d')
                                for grandchild in child.winfo_children():
                                    if isinstance(grandchild, tk.Label):
                                        grandchild.config(bg='#3d3d3d')

                    def on_leave(e):
                        album_frame.config(bg='#2d2d2d')
                        for child in album_frame.winfo_children():
                            if isinstance(child, tk.Frame):
                                child.config(bg='#2d2d2d')
                                for grandchild in child.winfo_children():
                                    if isinstance(grandchild, tk.Label):
                                        grandchild.config(bg='#2d2d2d')

                    album_frame.bind("<Enter>", on_enter)
                    album_frame.bind("<Leave>", on_leave)

                    # 加载现代化缩略图
                    if PILLOW_AVAILABLE:
                        try:
                            image = Image.open(album['cover'])
                            image = image.resize((220, 165), Image.Resampling.LANCZOS)
                            photo = ImageTk.PhotoImage(image)

                            # 图片容器
                            img_container = tk.Frame(album_frame, bg='#2d2d2d')
                            img_container.pack(pady=10)

                            # 图片标签
                            img_label = tk.Label(img_container, image=photo, bg='#2d2d2d',
                                               cursor='hand2')
                            img_label.image = photo  # 保持引用
                            img_label.pack()

                            # 绑定点击事件
                            img_label.bind("<Button-1>",
                                         lambda e, path=album['path']: self.open_album(path))

                        except Exception:
                            # 现代化占位符
                            placeholder_container = tk.Frame(album_frame, bg='#2d2d2d')
                            placeholder_container.pack(pady=10)

                            placeholder = tk.Label(placeholder_container,
                                                 text="🖼️\n无法加载缩略图",
                                                 bg='#444444', fg='#a0a0a0',
                                                 width=28, height=12,
                                                 font=('Segoe UI', 10),
                                                 cursor='hand2')
                            placeholder.pack()
                            placeholder.bind("<Button-1>",
                                           lambda e, path=album['path']: self.open_album(path))
                    else:
                        # Pillow不可用时的现代化占位符
                        placeholder_container = tk.Frame(album_frame, bg='#2d2d2d')
                        placeholder_container.pack(pady=10)

                        album_name = album['name'][:15] + "..." if len(album['name']) > 15 else album['name']
                        placeholder = tk.Label(placeholder_container,
                                             text=f"📁\n{album_name}",
                                             bg='#444444', fg='#a0a0a0',
                                             width=28, height=12,
                                             font=('Segoe UI', 10),
                                             cursor='hand2')
                        placeholder.pack()
                        placeholder.bind("<Button-1>",
                                       lambda e, path=album['path']: self.open_album(path))

                    # 现代化相册信息
                    info_frame = tk.Frame(album_frame, bg='#2d2d2d')
                    info_frame.pack(fill='x', padx=15, pady=(0, 15))

                    # 相册名称
                    album_name = album['name']
                    if len(album_name) > 25:
                        album_name = album_name[:22] + "..."

                    name_label = tk.Label(info_frame, text=album_name,
                                        bg='#2d2d2d', fg='#ffffff',
                                        font=('Segoe UI', 10, 'bold'),
                                        wraplength=200, justify='center')
                    name_label.pack(pady=(0, 5))

                    # 分类标签
                    if 'category' in album and album['category'] != '未分类':
                        category_label = tk.Label(info_frame, text=f"🏷️ {album['category']}",
                                                bg='#2d2d2d', fg='#ff9800',
                                                font=('Segoe UI', 8))
                        category_label.pack(pady=(0, 3))

                    # 图片数量和状态
                    count_frame = tk.Frame(info_frame, bg='#2d2d2d')
                    count_frame.pack()

                    count_label = tk.Label(count_frame, text=f"📷 {album['count']} 张",
                                         bg='#2d2d2d', fg='#00d4ff',
                                         font=('Segoe UI', 9))
                    count_label.pack(side='left')

                    # 状态指示
                    status_label = tk.Label(count_frame, text=" • 已完成",
                                          bg='#2d2d2d', fg='#4caf50',
                                          font=('Segoe UI', 9))
                    status_label.pack(side='left')

                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1

                except Exception as e:
                    self.log_message(f"创建相册卡片失败: {str(e)}", 'error')
                    continue

            # 更新画布滚动区域
            self.gallery_frame.update_idletasks()
            self.gallery_canvas.configure(scrollregion=self.gallery_canvas.bbox("all"))

            self.log_message(f"画廊加载完成，共 {len(albums)} 个相册", 'success')

        except Exception as e:
            self.log_message(f"加载画廊失败: {str(e)}", 'error')

    def open_album(self, album_path):
        """打开相册文件夹"""
        try:
            os.startfile(album_path)  # Windows
        except Exception as e:
            self.log_message(f"打开相册失败: {str(e)}", 'error')


def main():
    """主函数"""
    root = tk.Tk()
    app = PicSpiderGUI(root)

    # 启动时加载画廊
    root.after(1000, app.refresh_gallery)

    root.mainloop()


if __name__ == "__main__":
    main()
