#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试泰国分类URL的有效性
"""

import requests
from bs4 import BeautifulSoup

def test_thailand_category():
    """测试泰国分类页面"""
    url = 'https://everia.club/category/thailand/'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://everia.club/'
    }
    
    try:
        print(f"正在测试URL: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找缩略图链接
            thumbnail_links = soup.select('a.thumbnail-link')
            print(f"找到缩略图链接数量: {len(thumbnail_links)}")
            
            # 显示前几个链接的标题
            for i, link in enumerate(thumbnail_links[:5]):
                img_tag = link.find('img')
                if img_tag:
                    title = img_tag.get('title', '未命名')
                    href = link.get('href', '')
                    print(f"  {i+1}. {title}")
                    print(f"     链接: {href}")
            
            print("\n✅ 泰国分类测试成功！")
            return True
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== 泰国分类测试 ===")
    test_thailand_category()
