#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PicSpider GUI 启动脚本
检查依赖并启动GUI程序
"""

import sys
import subprocess
import importlib
import os

def check_and_install_dependencies():
    """检查并安装依赖"""
    required_packages = {
        'tkinter': 'tkinter (Python内置)',
        'requests': 'requests',
        'bs4': 'beautifulsoup4',
        'PIL': 'Pillow',
        'lxml': 'lxml'
    }
    
    missing_packages = []
    
    print("正在检查依赖包...")
    
    for package, pip_name in required_packages.items():
        try:
            if package == 'tkinter':
                import tkinter
            else:
                importlib.import_module(package)
            print(f"✓ {pip_name}")
        except ImportError:
            print(f"✗ {pip_name} - 缺失")
            if package != 'tkinter':
                missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n发现缺失的依赖包: {', '.join(missing_packages)}")
        response = input("是否自动安装缺失的依赖包? (y/n): ")
        
        if response.lower() in ['y', 'yes', '是']:
            print("正在安装依赖包...")
            try:
                subprocess.check_call([
                    sys.executable, '-m', 'pip', 'install'
                ] + missing_packages)
                print("依赖包安装完成！")
            except subprocess.CalledProcessError:
                print("依赖包安装失败，请手动安装:")
                print(f"pip install {' '.join(missing_packages)}")
                return False
        else:
            print("请手动安装缺失的依赖包:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("  PicSpider GUI - 写真爬虫图形界面")
    print("=" * 60)
    
    # 检查依赖
    if not check_and_install_dependencies():
        print("\n依赖检查失败，程序退出。")
        input("按回车键退出...")
        return
    
    print("\n依赖检查完成，启动GUI程序...")
    
    try:
        # 导入并启动GUI
        from picspider_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"导入GUI模块失败: {e}")
        print("请确保 picspider_gui.py 文件在当前目录中。")
        input("按回车键退出...")
    except Exception as e:
        print(f"程序运行出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
