#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PicSpider GUI 测试脚本
用于验证GUI程序的基本功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time

def test_basic_gui():
    """测试基本GUI功能"""
    root = tk.Tk()
    root.title("PicSpider GUI 测试")
    root.geometry("600x400")
    root.configure(bg='#2c2c2c')
    
    # 测试标签
    title_label = tk.Label(root, text="PicSpider GUI 测试程序", 
                          bg='#2c2c2c', fg='#00bcd4', 
                          font=('Arial', 16, 'bold'))
    title_label.pack(pady=20)
    
    # 测试按钮
    def test_function():
        messagebox.showinfo("测试", "GUI功能正常！")
    
    test_btn = tk.Button(root, text="测试按钮", 
                        command=test_function,
                        bg='#4caf50', fg='white', 
                        font=('Arial', 12))
    test_btn.pack(pady=10)
    
    # 测试文本框
    text_frame = tk.Frame(root, bg='#2c2c2c')
    text_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    text_widget = tk.Text(text_frame, bg='#1a1a1a', fg='#e0e0e0',
                         font=('Consolas', 10))
    text_widget.pack(fill='both', expand=True)
    
    # 添加测试文本
    test_text = """
PicSpider GUI 测试程序

✓ tkinter 界面正常
✓ 按钮功能正常
✓ 文本显示正常
✓ 颜色主题正常

如果您能看到这个界面，说明基本的GUI功能都正常工作。

现在可以尝试运行完整的 PicSpider GUI 程序：
python picspider_gui.py

或使用启动脚本：
python run_gui.py
"""
    
    text_widget.insert('1.0', test_text)
    text_widget.config(state='disabled')
    
    # 退出按钮
    quit_btn = tk.Button(root, text="退出测试", 
                        command=root.quit,
                        bg='#f44336', fg='white', 
                        font=('Arial', 12))
    quit_btn.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    print("启动 PicSpider GUI 测试程序...")
    test_basic_gui()
    print("测试完成！")
