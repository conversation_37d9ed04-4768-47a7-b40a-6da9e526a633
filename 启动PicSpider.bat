@echo off
title PicSpider GUI Launcher

:: Set color
color 0A

echo.
echo ========================================
echo    PicSpider GUI Launcher
echo ========================================
echo.
echo Checking runtime environment...

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python environment not found
    echo.
    echo Please install Python 3.7 or higher
    echo Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo [OK] Python environment check passed

:: 检查是否在正确目录
if not exist "picspider_gui.py" (
    echo ❌ 错误：未找到picspider_gui.py文件
    echo.
    echo 请确保此批处理文件与picspider_gui.py在同一目录下
    echo.
    pause
    exit /b 1
)

echo ✅ 程序文件检查通过

:: 检查依赖包
echo.
echo 正在检查依赖包...

python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：tkinter模块未找到
    echo.
    echo tkinter是Python的内置模块，请检查Python安装
    pause
    exit /b 1
)

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告：requests模块未安装
    echo.
    echo 正在自动安装依赖包...
    pip install requests beautifulsoup4 lxml
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo.
        echo 请手动执行：pip install -r requirements_gui.txt
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包检查通过
)

:: 检查Pillow（可选）
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  提示：Pillow未安装，图片预览功能将不可用
    echo.
    echo 是否现在安装Pillow？(Y/N)
    set /p install_pillow=
    if /i "%install_pillow%"=="Y" (
        echo 正在安装Pillow...
        pip install Pillow
        if errorlevel 1 (
            echo ⚠️  Pillow安装失败，但程序仍可正常运行
        ) else (
            echo ✅ Pillow安装成功
        )
    )
)

echo.
echo ========================================
echo    🚀 启动 PicSpider GUI
echo ========================================
echo.

:: 启动GUI程序
python picspider_gui.py

:: 检查程序退出状态
if errorlevel 1 (
    echo.
    echo ❌ 程序运行出现错误
    echo.
    echo 可能的解决方案：
    echo 1. 检查Python版本是否为3.7+
    echo 2. 确保所有依赖包已正确安装
    echo 3. 检查是否有杀毒软件阻止程序运行
    echo.
    pause
) else (
    echo.
    echo ✅ 程序正常退出
)

echo.
echo 感谢使用PicSpider GUI！
pause
