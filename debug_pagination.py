#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断分页问题的脚本
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import time

def debug_pagination():
    """调试分页功能"""
    
    # 测试URL
    test_url = 'https://everia.club/category/gravure/'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://everia.club/'
    }
    
    print("=== 分页调试分析 ===")
    print(f"测试URL: {test_url}")
    print()
    
    current_url = test_url
    page_count = 0
    max_test_pages = 10  # 只测试前10页
    
    while current_url and page_count < max_test_pages:
        try:
            print(f"📄 第 {page_count + 1} 页")
            print(f"URL: {current_url}")
            
            # 请求页面
            response = requests.get(current_url, headers=headers, timeout=10)
            print(f"HTTP状态: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                break
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查缩略图链接
            thumbnail_links = soup.select('a.thumbnail-link')
            print(f"缩略图链接数量: {len(thumbnail_links)}")
            
            # 分析分页元素
            print("\n🔍 分页元素分析:")
            
            # 检查所有可能的分页选择器
            pagination_selectors = [
                'a.next.page-numbers',
                'a.next',
                '.page-numbers.next',
                '.pagination .next',
                'a[rel="next"]',
                '.nav-links .next',
                '.pagination-next',
                'a:contains("下一页")',
                'a:contains("Next")',
                'a:contains("»")'
            ]
            
            next_link = None
            working_selector = None
            
            for selector in pagination_selectors:
                try:
                    if selector.startswith('a:contains'):
                        # BeautifulSoup不支持:contains，跳过
                        continue
                    
                    elements = soup.select(selector)
                    if elements:
                        print(f"  ✅ 找到元素: {selector} ({len(elements)}个)")
                        if not next_link:
                            next_link = elements[0]
                            working_selector = selector
                    else:
                        print(f"  ❌ 未找到: {selector}")
                except Exception as e:
                    print(f"  ⚠️ 选择器错误 {selector}: {str(e)}")
            
            # 检查包含"下一页"或"Next"文本的链接
            print("\n🔍 文本匹配分析:")
            all_links = soup.find_all('a')
            for link in all_links:
                link_text = link.get_text().strip()
                if any(keyword in link_text.lower() for keyword in ['next', '下一页', '»', '>']):
                    print(f"  📎 找到可能的下一页链接: '{link_text}' -> {link.get('href', 'No href')}")
                    if not next_link:
                        next_link = link
                        working_selector = f"文本匹配: '{link_text}'"
            
            # 检查分页容器
            print("\n🔍 分页容器分析:")
            pagination_containers = soup.select('.pagination, .page-numbers, .nav-links, .wp-pagenavi')
            for container in pagination_containers:
                print(f"  📦 分页容器: {container.name} class='{container.get('class', [])}'")
                links = container.find_all('a')
                for link in links:
                    link_text = link.get_text().strip()
                    link_href = link.get('href', '')
                    print(f"    🔗 链接: '{link_text}' -> {link_href}")
            
            if next_link:
                next_href = next_link.get('href')
                if next_href:
                    next_url = urljoin(current_url, next_href)
                    print(f"\n✅ 找到下一页链接:")
                    print(f"   选择器: {working_selector}")
                    print(f"   链接文本: '{next_link.get_text().strip()}'")
                    print(f"   原始href: {next_href}")
                    print(f"   完整URL: {next_url}")
                    
                    current_url = next_url
                else:
                    print(f"\n❌ 下一页链接没有href属性")
                    break
            else:
                print(f"\n❌ 未找到下一页链接")
                
                # 输出页面的HTML片段用于调试
                print("\n📝 页面HTML片段 (分页相关):")
                pagination_html = soup.find_all(['div', 'nav'], class_=lambda x: x and any(
                    keyword in ' '.join(x).lower() for keyword in ['page', 'nav', 'pagination']
                ))
                for element in pagination_html[:3]:  # 只显示前3个
                    print(f"   {element}")
                
                break
            
            page_count += 1
            print(f"\n{'='*50}")
            time.sleep(1)  # 避免请求过快
            
        except Exception as e:
            print(f"❌ 处理第 {page_count + 1} 页时出错: {str(e)}")
            break
    
    print(f"\n📊 测试总结:")
    print(f"成功处理页数: {page_count}")
    print(f"最后URL: {current_url}")
    
    if page_count < 5:
        print("\n⚠️ 警告: 分页功能可能存在问题!")
        print("建议检查:")
        print("1. 分页选择器是否正确")
        print("2. 网站结构是否发生变化")
        print("3. 是否有反爬虫机制")
    else:
        print("\n✅ 分页功能基本正常")

if __name__ == "__main__":
    debug_pagination()
