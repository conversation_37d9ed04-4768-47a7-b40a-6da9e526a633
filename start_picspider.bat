@echo off
title PicSpider GUI Launcher

:: Set color to green
color 0A

echo.
echo ========================================
echo    PicSpider GUI Launcher
echo ========================================
echo.

echo Checking runtime environment...

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python environment not found
    echo.
    echo Please install Python 3.7 or higher
    echo Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo [OK] Python environment check passed

:: Check if program file exists
if not exist "picspider_gui.py" (
    echo [ERROR] picspider_gui.py file not found
    echo.
    echo Please ensure this batch file is in the same directory as picspider_gui.py
    echo.
    pause
    exit /b 1
)

echo [OK] Program file check passed

:: Check dependencies
echo.
echo Checking dependencies...

python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] tkinter module not found
    echo.
    echo tkinter is a built-in Python module, please check your Python installation
    pause
    exit /b 1
)

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] requests module not installed
    echo.
    echo Installing dependencies automatically...
    pip install requests beautifulsoup4 lxml
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        echo.
        echo Please manually run: pip install -r requirements_gui.txt
        pause
        exit /b 1
    )
    echo [OK] Dependencies installed successfully
) else (
    echo [OK] Dependencies check passed
)

:: Check Pillow (optional)
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Pillow not installed, image preview will not be available
    echo.
    echo Do you want to install Pillow now? (Y/N)
    set /p install_pillow=
    if /i "%install_pillow%"=="Y" (
        echo Installing Pillow...
        pip install Pillow
        if errorlevel 1 (
            echo [WARNING] Pillow installation failed, but the program can still run
        ) else (
            echo [OK] Pillow installed successfully
        )
    )
)

echo.
echo ========================================
echo    Starting PicSpider GUI
echo ========================================
echo.

:: Start GUI program
python picspider_gui.py

:: Check program exit status
if errorlevel 1 (
    echo.
    echo [ERROR] Program encountered an error
    echo.
    echo Possible solutions:
    echo 1. Check if Python version is 3.7+
    echo 2. Ensure all dependencies are correctly installed
    echo 3. Check if antivirus software is blocking the program
    echo.
    pause
) else (
    echo.
    echo [OK] Program exited normally
)

echo.
echo Thank you for using PicSpider GUI!
pause
