# 🚀 PicSpider GUI 快速使用指南

## ⚡ 快速开始 (3步搞定)

### 第1步：安装依赖
双击运行：`install.bat`
```
这将自动安装所有需要的Python包
```

### 第2步：启动程序
双击运行：`run.bat`
```
这将启动PicSpider GUI程序
```

### 第3步：开始使用
在GUI界面中：
1. 点击"⚙️ 设置"标签页，设置爬取页数为1-2页（测试用）
2. 点击"🚀 爬虫控制台"标签页
3. 勾选一个分类（建议选择"🌸 写真精选"）
4. 点击"🚀 开始爬取"按钮
5. 观察日志输出和下载进度

## 📁 重要文件说明

### 🎯 推荐使用的文件
- **`run.bat`** ⭐ - 最简单的启动方式
- **`install.bat`** ⭐ - 依赖安装脚本
- **`start_picspider.bat`** - 完整的启动检查器

### 📚 文档文件
- **`HOW_TO_USE.md`** - 本文件，快速使用指南
- **`GUI_README.md`** - 详细使用说明
- **`TESTING_GUIDE.md`** - 完整测试指南

### 🔧 程序文件
- **`picspider_gui.py`** - 主程序文件
- **`requirements_gui.txt`** - 依赖包列表

## ⚠️ 常见问题

### Q: 双击bat文件没反应？
A: 右键选择"以管理员身份运行"

### Q: 提示Python未找到？
A: 请先安装Python 3.7+，下载地址：https://www.python.org/downloads/

### Q: 安装依赖失败？
A: 手动运行：`pip install flask requests beautifulsoup4 lxml Pillow`

### Q: 程序启动后界面乱码？
A: 确保系统支持UTF-8编码，或使用英文版Windows

### Q: 下载速度慢？
A: 在设置中降低并发数到3-5个

## 🎯 使用建议

### 💡 首次使用
1. 先运行 `install.bat` 安装依赖
2. 再运行 `run.bat` 启动程序
3. 设置较小的页数进行测试

### 💡 日常使用
1. 直接双击 `run.bat` 启动
2. 根据需要调整设置参数
3. 选择合适的分类进行爬取

### 💡 故障排除
1. 如遇问题，运行 `start_picspider.bat` 进行完整检查
2. 查看程序内的彩色日志信息
3. 参考详细文档 `GUI_README.md`

## 🎊 享受使用！

PicSpider GUI是一个功能完整的现代化桌面应用程序，具有：
- 🎨 现代化深色主题界面
- ⚡ 实时爬取控制和进度显示
- 🖼️ 内置图片浏览功能
- ⚙️ 灵活的参数配置
- 📝 彩色日志系统

**现在就开始体验吧！** 🚀
